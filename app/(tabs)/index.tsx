import * as React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, ActivityIndicator, RefreshControl } from 'react-native';
import { Appbar, Card, Text, Badge } from 'react-native-paper';
import LoadingAnimation from '@/app/components/LoadingAnimation';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'react-native';
import { Colors } from '../../constants/Colors';
import createRecipeStyles from '@/app/styles/RecipeStyles';
import ShoppingCart from '../../assets/images/icons/shopping-cart.svg';
import Camera from '../../assets/images/icons/camera.svg';
import { auth } from '../../firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { Recipe, InventoryItem, DietPreference, numRecipesPerMealTypeInitialState } from '../components/types';
import { RecipeVariant, InstructionType } from '../components/types';
import { MealType, MealTypeItem } from '../components/types';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import RecipeCard from '../components/RecipeCard';
import { getThemeColors } from '@/app/styles/Theme';
import ExpandedRecipeCard from '../components/ExpandedRecipeCard';
import { generateRecipeBasicsAsync, generateRecipeDetailsAsync } from '../services/generateRecipes';
import { RecipeService } from '../services/RecipeService';
import { FilterMealType } from '../components/types';
import { useGroceryList } from '@/app/contexts/GroceryListContext';

export default function RecipeTab() {
  const colorScheme = useColorScheme() || 'light';
  const router = useRouter();
  const recipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const [expandedRecipeIds, setExpandedRecipeIds] = useState<Set<string>>(new Set());
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [servings, setServings] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<RecipeVariant>('Basic');
  const [instructionType, setInstructionType] = useState<InstructionType>(InstructionType.HIGH_LEVEL);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedMealType, setSelectedMealType] = useState<FilterMealType>('All');
  const [recipesPerMealType, setRecipesPerMealType] = useState<{ [key in MealType]: number }>(
    numRecipesPerMealTypeInitialState
  );
  const [lastResponseId, setLastResponseId] = useState<string | null>(null);
  const [loadingRecipeDetails, setLoadingRecipeDetails] = useState<string | null>(null);
  const [loadedDetailRecipeIds, setLoadedDetailRecipeIds] = useState<Set<string>>(new Set());
  const { groceryItemCount } = useGroceryList();
  const MAX_RECIPES_PER_MEAL_TYPE = 4;

  // Fetch recipes from both LLM and Firestore
  const getRecipes = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Fetch inventory document
      const inventoryDoc = await firestoreRepository.getDocument(FirestoreCollections.INVENTORY, user.uid);

      // Get items array from document
      const inventoryItems = (inventoryDoc?.items || []) as InventoryItem[];

      // Transform to ingredients
      const ingredients = inventoryItems
        .filter((item) => item.name && item.quantity > 0)
        .map((item) => ({
          name: item.name,
          available: true,
        }));

      // Fetch diet preferences
      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      // Reset recipes per meal type
      setRecipesPerMealType(numRecipesPerMealTypeInitialState);

      // Reset loaded recipe details
      setLoadedDetailRecipeIds(new Set());

      // Use parallel fetching from both LLM and Firestore
      const llmFetchFunction = () =>
        generateRecipeBasicsAsync(
          ingredients,
          dietPrefsDoc,
          numRecipesPerMealTypeInitialState,
          recipes.map((r) => r.title)
        );

      const result = await RecipeService.fetchRecipesParallel(user.uid, llmFetchFunction);

      console.log(`Recipes loaded from ${result.source}:`, result.recipes.length);

      // Store the response ID for later use when fetching details (if available from LLM)
      if (result.responseId) {
        setLastResponseId(result.responseId);
      }

      setRecipes(result.recipes);
    } catch (error) {
      console.error('Error updating recipes:', error);
      setError('Failed to update recipes');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const loadMoreRecipes = async () => {
    try {
      // Don't load more if already at max recipes for the current meal type
      if (selectedMealType !== 'All' && recipesPerMealType[selectedMealType as MealType] >= MAX_RECIPES_PER_MEAL_TYPE) {
        return;
      }

      // Don't load more if all meal types have reached their max
      if (
        selectedMealType === 'All' &&
        Object.values(MealType).every((type) => recipesPerMealType[type] >= MAX_RECIPES_PER_MEAL_TYPE)
      ) {
        return;
      }

      setLoadingMore(true);
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Fetch inventory document
      const inventoryDoc = await firestoreRepository.getDocument(FirestoreCollections.INVENTORY, user.uid);

      // Get items array from document
      const inventoryItems = (inventoryDoc?.items || []) as InventoryItem[];

      // Transform to ingredients
      const ingredients = inventoryItems
        .filter((item) => item.name && item.quantity > 0)
        .map((item) => ({
          name: item.name,
          available: true,
        }));

      // Fetch diet preferences
      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      // Generate more recipes
      if (ingredients.length > 0) {
        // Update the count for each meal type that needs more recipes
        const updatedCounts = { ...recipesPerMealType };

        if (selectedMealType === 'All') {
          // If "All" is selected, add 1 to each meal type that hasn't reached max
          Object.values(MealType).forEach((mealType) => {
            if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
              updatedCounts[mealType]++;
            }
          });
        } else {
          // Otherwise just add to the selected meal type
          const mealType = selectedMealType as MealType;
          if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
            updatedCounts[mealType]++;
          }
        }

        setRecipesPerMealType(updatedCounts);

        // Generate new recipes
        let newRecipes: Recipe[] = [];

        if (selectedMealType === 'All') {
          // Generate recipes for each meal type that isn't at max
          for (const [mealTypeKey, mealType] of Object.entries(MealType)) {
            if (recipesPerMealType[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
              // Generate one recipe per meal type that needs more
              const { recipes: typeRecipes, responseId } = await generateRecipeBasicsAsync(
                ingredients,
                dietPrefsDoc,
                {
                  [mealTypeKey]: 1,
                },
                recipes.map((r) => r.title)
              );

              // Update the response ID
              setLastResponseId(responseId);

              // Try to find a recipe of the correct meal type
              const matchingRecipe = typeRecipes.find((r) => r.mealType === mealType);
              if (matchingRecipe) {
                // If we found one, use it
                newRecipes.push(matchingRecipe);
              } else {
                // Otherwise, modify the first recipe to match the needed meal type
                const modifiedRecipe = { ...typeRecipes[0], mealType: mealType };
                newRecipes.push(modifiedRecipe);
              }
            }
          }
        } else {
          // Generate recipes for the selected meal type
          const { recipes: generatedRecipes, responseId } = await generateRecipeBasicsAsync(
            ingredients,
            dietPrefsDoc,
            {
              [selectedMealType]: 1,
            },
            recipes.map((r) => r.title)
          );

          // Update the response ID
          setLastResponseId(responseId);
          newRecipes = generatedRecipes;

          // Ensure the recipe is of the right meal type
          newRecipes = newRecipes.map((recipe) => ({
            ...recipe,
            mealType: selectedMealType as MealType,
          }));
        }

        // Combine with existing recipes
        setRecipes((prevRecipes) => {
          // Create a new array with unique recipes by ID
          const combinedRecipes = [...prevRecipes];
          newRecipes.forEach((newRecipe) => {
            // Check if this recipe ID already exists
            if (!combinedRecipes.some((r) => r.id === newRecipe.id)) {
              combinedRecipes.push(newRecipe);
            }
          });
          return combinedRecipes;
        });
      }
    } catch (error) {
      console.error('Error loading more recipes:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getRecipes();
  }, []);

  // Initial fetch
  useEffect(() => {
    getRecipes();
  }, []);

  const mealTypes: MealTypeItem[] = [
    { title: 'All', icon: 'food-fork-drink' },
    { title: MealType.BREAKFAST, icon: 'egg-fried' },
    { title: MealType.LUNCH, icon: 'rice' },
    { title: MealType.DINNER, icon: 'pot-steam' },
    { title: MealType.SNACKS, icon: 'food-apple' },
    { title: MealType.DESSERT, icon: 'cupcake' },
  ];

  const recipeVariants: RecipeVariant[] = ['Basic', 'Fancy', 'Chef curated'];
  const instructionTypes: InstructionType[] = [
    InstructionType.HIGH_LEVEL,
    InstructionType.DETAILED,
    InstructionType.TEACH_MODE,
  ];

  const toggleExpanded = async (recipeId: string) => {
    // If we're already expanded, just collapse
    if (expandedRecipeIds.has(recipeId)) {
      setExpandedRecipeIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.delete(recipeId);
        return newIds;
      });
      return;
    }

    // First, expand the recipe immediately to show the loading skeleton
    setExpandedRecipeIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.add(recipeId);
      return newIds;
    });

    // If we need to fetch details, do it after expanding
    if (!loadedDetailRecipeIds.has(recipeId) && lastResponseId) {
      setLoadingRecipeDetails(recipeId);

      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('User not authenticated');
        }

        // Find the recipe
        const recipe = recipes.find((r) => r.id === recipeId);
        if (!recipe) {
          throw new Error('Recipe not found');
        }

        // Fetch recipe details using the previous response ID
        const details = await generateRecipeDetailsAsync(recipe.id, recipe.title, recipe.mealType, lastResponseId);

        // Update the recipe with the details
        setRecipes((prevRecipes) =>
          prevRecipes.map((r) =>
            r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
          )
        );

        // Mark this recipe as having loaded details
        setLoadedDetailRecipeIds((prev) => {
          const newSet = new Set(prev);
          newSet.add(recipeId);
          return newSet;
        });
      } catch (error) {
        console.error('Error loading recipe details:', error);
      } finally {
        setLoadingRecipeDetails(null);
      }
    }
  };

  const toggleFavorite = (recipeId: string, e: any) => {
    e.stopPropagation();
    const newFavorites = new Set(favorites);
    if (favorites.has(recipeId)) {
      newFavorites.delete(recipeId);
    } else {
      newFavorites.add(recipeId);
    }
    setFavorites(newFavorites);
  };

  const filteredRecipes = recipes.filter((recipe) => {
    // Filter by meal type
    if (selectedMealType !== 'All' && recipe.mealType !== selectedMealType) {
      return false;
    }

    // Limit the number of recipes per meal type
    const mealTypeRecipes = recipes.filter((r) => r.mealType === recipe.mealType);
    const thisRecipeIndex = mealTypeRecipes.findIndex((r) => r.id === recipe.id);
    return thisRecipeIndex < recipesPerMealType[recipe.mealType];
  });

  // Check if we can load more for the current selection
  const canLoadMore =
    selectedMealType === 'All'
      ? Object.values(MealType).some((type) => recipesPerMealType[type] < MAX_RECIPES_PER_MEAL_TYPE)
      : recipesPerMealType[selectedMealType as MealType] < MAX_RECIPES_PER_MEAL_TYPE;

  const renderRecipeCard = (recipe: Recipe) => {
    const isExpanded = expandedRecipeIds.has(recipe.id);
    const isFavorite = favorites.has(recipe.id);
    const isLoadingDetails = loadingRecipeDetails === recipe.id;

    if (isExpanded) {
      return (
        <View key={recipe.id} style={recipeStyles.recipeCardContainer}>
          <ExpandedRecipeCard
            title={recipe.title}
            timeInMinutes={recipe.timeInMinutes}
            calories={recipe.calories}
            imageUrl={recipe.imageUrl}
            instructions={recipe.instructions}
            ingredients={recipe.ingredients}
            isFavorite={isFavorite}
            servings={servings}
            selectedVariant={selectedVariant}
            instructionType={instructionType}
            recipeVariants={recipeVariants}
            instructionTypes={instructionTypes}
            isLoading={isLoadingDetails}
            onToggleFavorite={(e: any) => toggleFavorite(recipe.id, e)}
            onSelectVariant={setSelectedVariant}
            onSelectInstructionType={setInstructionType}
            onChangeServings={setServings}
            onPress={() => toggleExpanded(recipe.id)}
          />
        </View>
      );
    }

    return (
      <View key={recipe.id} style={recipeStyles.recipeCardContainer}>
        <RecipeCard
          title={recipe.title}
          timeInMinutes={recipe.timeInMinutes}
          calories={recipe.calories}
          imageUrl={recipe.imageUrl}
          isFavorite={isFavorite}
          onToggleFavorite={(e: any) => toggleFavorite(recipe.id, e)}
          onPress={() => toggleExpanded(recipe.id)}
        />
      </View>
    );
  };

  return (
    <View style={recipeStyles.container}>
      <Appbar.Header style={recipeStyles.header}>
        <Appbar.Action icon={() => <Camera fill={Colors[colorScheme].tint} />} onPress={() => router.push('/camera')} />
        <Appbar.Content title='Recipes' style={recipeStyles.title} />
        <View>
          <Appbar.Action
            icon={() => <ShoppingCart fill={Colors[colorScheme].tint} />}
            onPress={() => router.push('/grocery-list')}
          />
          {groceryItemCount > 0 && (
            <Badge
              visible={true}
              size={20}
              style={{
                position: 'absolute',
                top: 5,
                right: 5,
                backgroundColor: colors.accent,
              }}
            >
              {groceryItemCount}
            </Badge>
          )}
        </View>
      </Appbar.Header>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={recipeStyles.mainScrollContainer}
        refreshControl={
          <RefreshControl refreshing={false} onRefresh={onRefresh} colors={[colors.accent]} tintColor={colors.accent} />
        }
        onScroll={({ nativeEvent }) => {
          // Check if user has scrolled to the bottom
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;
          if (
            layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom &&
            !loadingMore &&
            canLoadMore
          ) {
            console.log('>>>>>loading more');
            loadMoreRecipes();
          }
        }}
        scrollEventThrottle={400}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={recipeStyles.categoryScrollContainer}
        >
          {mealTypes.map((meal) => (
            <Card
              key={meal.title}
              style={[recipeStyles.categoryCard, selectedMealType === meal.title && { backgroundColor: colors.accent }]}
              onPress={() => setSelectedMealType(selectedMealType === meal.title ? 'All' : meal.title)}
            >
              <View style={recipeStyles.categoryCardContent}>
                <MaterialCommunityIcons
                  name={meal.icon}
                  size={24}
                  color={selectedMealType === meal.title ? colors.accentText : colors.text}
                  style={recipeStyles.icon}
                />
                <Text
                  style={[
                    recipeStyles.categoryCardTitle,
                    selectedMealType === meal.title ? { color: colors.accentText } : { color: colors.text },
                  ]}
                >
                  {meal.title}
                </Text>
              </View>
            </Card>
          ))}
        </ScrollView>

        {/* Error States */}
        {error && (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
          </View>
        )}

        {(isLoading || refreshing) && (
          <View style={recipeStyles.loadingContainer}>
            <LoadingAnimation
              source={require('../../assets/images/gifs/fry-pan.gif')}
              message='Loading delicious recipes for you...'
            />
          </View>
        )}

        {/* Recipe Cards */}
        {!isLoading && !error && filteredRecipes.map(renderRecipeCard)}

        {/* Load More Indicator */}
        {canLoadMore && !isLoading && !refreshing && !error && (
          <View style={{ padding: 20, alignItems: 'center' }}>
            {loadingMore ? (
              <ActivityIndicator size='small' color={colors.accent} />
            ) : (
              <Text style={{ color: colors.text }}>Scroll for more recipes</Text>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}
