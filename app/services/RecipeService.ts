import { Recipe, MealType, InstructionType } from '@/app/components/types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { UnsplashService } from '@/app/services/UnsplashService';

/**
 * Service for fetching recipes from various sources
 */
export class RecipeService {
  /**
   * Fetch recipes from Firestore for a specific user
   *
   * @param userId The user's UID
   * @returns Array of recipes from Firestore, or empty array if none found
   */
  static async fetchRecipesFromFirestore(userId: string): Promise<Recipe[]> {
    try {
      console.log('Fetching recipes from Firestore for user:', userId);

      // Fetch the recipes document for this user
      const recipesDoc = await firestoreRepository.getDocument(FirestoreCollections.RECIPES, userId);

      if (!recipesDoc || !recipesDoc.recipes || !Array.isArray(recipesDoc.recipes)) {
        console.log('No recipes found in Firestore for user:', userId);
        return [];
      }

      const firestoreRecipes = recipesDoc.recipes;
      console.log(`Found ${firestoreRecipes.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        firestoreRecipes.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            ingredients: recipe.ingredients || [],
            instructions: recipe.instructions || {
              [InstructionType.HIGH_LEVEL]: 'Instructions not available',
              [InstructionType.DETAILED]: 'Instructions not available',
              [InstructionType.TEACH_MODE]: 'Instructions not available',
            },
            mealType: recipe.mealType || MealType.BREAKFAST,
          };
        })
      );

      return transformedRecipes;
    } catch (error) {
      console.error('Error fetching recipes from Firestore:', error);
      return [];
    }
  }

  /**
   * Fetch recipes from both LLM and Firestore in parallel
   * Returns whichever source responds first, with the other as a fallback
   *
   * @param userId The user's UID
   * @param llmFetchFunction Function to fetch recipes from LLM
   * @returns Object containing recipes and source information
   */
  static async fetchRecipesParallel(
    userId: string,
    llmFetchFunction: () => Promise<{ recipes: Recipe[]; responseId: string }>
  ): Promise<{
    recipes: Recipe[];
    source: 'llm' | 'firestore' | 'both';
    responseId?: string;
    firestoreRecipes?: Recipe[];
    llmRecipes?: Recipe[];
  }> {
    console.log('Starting parallel recipe fetch for user:', userId);

    // Start both requests simultaneously
    const firestorePromise = this.fetchRecipesFromFirestore(userId);
    const llmPromise = llmFetchFunction();

    try {
      // Use Promise.allSettled to handle both promises regardless of individual failures
      const results = await Promise.allSettled([firestorePromise, llmPromise]);

      const firestoreResult = results[0];
      const llmResult = results[1];

      // Extract successful results
      const firestoreRecipes = firestoreResult.status === 'fulfilled' ? firestoreResult.value : [];
      const llmData = llmResult.status === 'fulfilled' ? llmResult.value : null;
      const llmRecipes = llmData?.recipes || [];

      console.log('Firestore recipes count:', firestoreRecipes.length);
      console.log('LLM recipes count:', llmRecipes.length);

      // Determine which source to use based on availability and timing
      if (firestoreRecipes.length > 0 && llmRecipes.length > 0) {
        // Both sources have recipes - prioritize Firestore for faster display
        // but include LLM recipes as additional options
        const combinedRecipes = [...firestoreRecipes, ...llmRecipes];

        // Remove duplicates based on title (case-insensitive)
        const uniqueRecipes = combinedRecipes.filter((recipe, index, array) =>
          array.findIndex(r => r.title.toLowerCase() === recipe.title.toLowerCase()) === index
        );

        return {
          recipes: uniqueRecipes,
          source: 'both',
          responseId: llmData?.responseId,
          firestoreRecipes,
          llmRecipes,
        };
      } else if (firestoreRecipes.length > 0) {
        // Only Firestore has recipes
        return {
          recipes: firestoreRecipes,
          source: 'firestore',
          firestoreRecipes,
          llmRecipes: [],
        };
      } else if (llmRecipes.length > 0) {
        // Only LLM has recipes
        return {
          recipes: llmRecipes,
          source: 'llm',
          responseId: llmData?.responseId,
          firestoreRecipes: [],
          llmRecipes,
        };
      } else {
        // Neither source has recipes
        console.warn('No recipes available from either source');
        return {
          recipes: [],
          source: 'both',
          firestoreRecipes: [],
          llmRecipes: [],
        };
      }
    } catch (error) {
      console.error('Error in parallel recipe fetch:', error);

      // Fallback: try each source individually
      try {
        const firestoreRecipes = await this.fetchRecipesFromFirestore(userId);
        if (firestoreRecipes.length > 0) {
          return {
            recipes: firestoreRecipes,
            source: 'firestore',
            firestoreRecipes,
            llmRecipes: [],
          };
        }
      } catch (firestoreError) {
        console.error('Firestore fallback failed:', firestoreError);
      }

      try {
        const llmData = await llmFetchFunction();
        return {
          recipes: llmData.recipes,
          source: 'llm',
          responseId: llmData.responseId,
          firestoreRecipes: [],
          llmRecipes: llmData.recipes,
        };
      } catch (llmError) {
        console.error('LLM fallback failed:', llmError);
      }

      // If all else fails, return empty
      return {
        recipes: [],
        source: 'both',
        firestoreRecipes: [],
        llmRecipes: [],
      };
    }
  }
}
