import { Recipe, MealType, InstructionType } from '@/app/components/types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { UnsplashService } from '@/app/services/UnsplashService';

/**
 * Service for fetching recipes from various sources
 */
export class RecipeService {
  /**
   * Fetch recipes from Firestore for a specific user
   *
   * @param userId The user's UID
   * @returns Array of recipes from Firestore, or empty array if none found
   */
  static async fetchRecipesFromFirestore(userId: string): Promise<Recipe[]> {
    try {
      console.log('Fetching recipes from Firestore for user:', userId);

      // Fetch the recipes document for this user
      const recipesDoc = await firestoreRepository.getDocument(FirestoreCollections.RECIPES, userId);

      if (!recipesDoc || !recipesDoc.recipes || !Array.isArray(recipesDoc.recipes)) {
        console.log('No recipes found in Firestore for user:', userId);
        return [];
      }

      const firestoreRecipes = recipesDoc.recipes;
      console.log(`Found ${firestoreRecipes.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        firestoreRecipes.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Transform instructions from Python format to TypeScript format
          let instructions = {
            [InstructionType.HIGH_LEVEL]: 'Instructions not available',
            [InstructionType.DETAILED]: 'Instructions not available',
            [InstructionType.TEACH_MODE]: 'Instructions not available',
          };

          if (recipe.instructions) {
            instructions = {
              [InstructionType.HIGH_LEVEL]: recipe.instructions['High level'] || recipe.instructions[InstructionType.HIGH_LEVEL] || 'Instructions not available',
              [InstructionType.DETAILED]: recipe.instructions['Detailed'] || recipe.instructions[InstructionType.DETAILED] || 'Instructions not available',
              [InstructionType.TEACH_MODE]: recipe.instructions['Teach mode'] || recipe.instructions[InstructionType.TEACH_MODE] || 'Instructions not available',
            };
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            ingredients: recipe.ingredients || [],
            instructions,
            mealType: recipe.mealType || MealType.BREAKFAST,
          };
        })
      );

      return transformedRecipes;
    } catch (error) {
      console.error('Error fetching recipes from Firestore:', error);
      return [];
    }
  }

  /**
   * Fetch recipes from both LLM and Firestore in parallel with progressive loading
   * Returns whichever source responds first, then adds the second source when it completes
   *
   * @param userId The user's UID
   * @param llmFetchFunction Function to fetch recipes from LLM
   * @param onFirstResult Callback when first source returns (for immediate UI update)
   * @param onSecondResult Callback when second source returns (for adding more recipes)
   * @returns Promise that resolves when both sources complete or fail
   */
  static async fetchRecipesProgressively(
    userId: string,
    llmFetchFunction: () => Promise<{ recipes: Recipe[]; responseId: string }>,
    onFirstResult: (recipes: Recipe[], source: 'llm' | 'firestore', responseId?: string) => void,
    onSecondResult: (recipes: Recipe[], source: 'llm' | 'firestore', responseId?: string) => void
  ): Promise<{
    firestoreRecipes: Recipe[];
    llmRecipes: Recipe[];
    llmResponseId?: string;
  }> {
    console.log('Starting progressive recipe fetch for user:', userId);

    // Start both requests simultaneously
    const firestorePromise = this.fetchRecipesFromFirestore(userId);
    const llmPromise = llmFetchFunction();

    let firstResultDelivered = false;
    let firestoreRecipes: Recipe[] = [];
    let llmRecipes: Recipe[] = [];
    let llmResponseId: string | undefined;

    // Race to see which completes first
    const racePromise = Promise.race([
      firestorePromise.then(recipes => ({ source: 'firestore' as const, recipes, responseId: undefined })),
      llmPromise.then(data => ({ source: 'llm' as const, recipes: data.recipes, responseId: data.responseId }))
    ]);

    try {
      // Handle the first result
      const firstResult = await racePromise;

      if (firstResult.recipes.length > 0) {
        onFirstResult(firstResult.recipes, firstResult.source, firstResult.responseId);
        firstResultDelivered = true;

        if (firstResult.source === 'firestore') {
          firestoreRecipes = firstResult.recipes;
        } else {
          llmRecipes = firstResult.recipes;
          llmResponseId = firstResult.responseId;
        }
      }

      // Now wait for both to complete to get the second result
      const results = await Promise.allSettled([firestorePromise, llmPromise]);

      const firestoreResult = results[0];
      const llmResult = results[1];

      // Process Firestore result if not already processed
      if (firestoreResult.status === 'fulfilled' && firestoreRecipes.length === 0) {
        firestoreRecipes = firestoreResult.value;
        if (!firstResultDelivered && firestoreRecipes.length > 0) {
          onFirstResult(firestoreRecipes, 'firestore');
          firstResultDelivered = true;
        } else if (firstResultDelivered && firestoreRecipes.length > 0) {
          onSecondResult(firestoreRecipes, 'firestore');
        }
      }

      // Process LLM result if not already processed
      if (llmResult.status === 'fulfilled' && llmRecipes.length === 0) {
        const llmData = llmResult.value;
        llmRecipes = llmData.recipes;
        llmResponseId = llmData.responseId;

        if (!firstResultDelivered && llmRecipes.length > 0) {
          onFirstResult(llmRecipes, 'llm', llmResponseId);
          firstResultDelivered = true;
        } else if (firstResultDelivered && llmRecipes.length > 0) {
          onSecondResult(llmRecipes, 'llm', llmResponseId);
        }
      }

      // If no results were delivered, try to deliver something
      if (!firstResultDelivered) {
        if (firestoreRecipes.length > 0) {
          onFirstResult(firestoreRecipes, 'firestore');
        } else if (llmRecipes.length > 0) {
          onFirstResult(llmRecipes, 'llm', llmResponseId);
        }
      }

    } catch (error) {
      console.error('Error in progressive recipe fetch:', error);

      // Fallback: try each source individually
      if (!firstResultDelivered) {
        try {
          firestoreRecipes = await this.fetchRecipesFromFirestore(userId);
          if (firestoreRecipes.length > 0) {
            onFirstResult(firestoreRecipes, 'firestore');
            firstResultDelivered = true;
          }
        } catch (firestoreError) {
          console.error('Firestore fallback failed:', firestoreError);
        }

        if (!firstResultDelivered) {
          try {
            const llmData = await llmFetchFunction();
            llmRecipes = llmData.recipes;
            llmResponseId = llmData.responseId;
            if (llmRecipes.length > 0) {
              onFirstResult(llmRecipes, 'llm', llmResponseId);
            }
          } catch (llmError) {
            console.error('LLM fallback failed:', llmError);
          }
        }
      }
    }

    return {
      firestoreRecipes,
      llmRecipes,
      llmResponseId,
    };
  }
}
